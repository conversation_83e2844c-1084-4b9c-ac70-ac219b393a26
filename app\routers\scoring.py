from fastapi import APIRouter, HTTPException
from app.schemas.request import ScoreRequest
from app.schemas.response import ScoreResponse
from app.models.scoring import compute_risk_score
from app.utils.batch_processor import BatchProcessor
from app.validation.transaction_validator import TransactionValidationError

router = APIRouter(prefix="/score", tags=["scoring"])

BATCH_THRESHOLD = 1000  # Process in batches if more than 1000 transactions

@router.post("/", response_model=ScoreResponse)
async def score(request: ScoreRequest):
    """
    Calculate risk score from transaction data.
    
    For large transaction sets (>1000), uses batch processing.
    Includes input validation and error handling.
    """
    try:
        if len(request.transactions) > BATCH_THRESHOLD:
            processor = BatchProcessor(batch_size=1000)
            result = processor.process_transactions(
                user_id=request.user_id,
                transactions=request.transactions
            )
        else:
            result = compute_risk_score(request)
        return result
        
    except TransactionValidationError as e:
        raise HTTPException(
            status_code=400,
            detail=f"Invalid transaction data: {str(e)}"
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Error calculating risk score: {str(e)}"
        )
