"""
Batch processing module for handling large transaction sets efficiently.
"""
from typing import List, Dict, Any
import pandas as pd
from concurrent.futures import ThreadPoolExecutor, as_completed
from app.validation.transaction_validator import validate_transactions
from app.models.scoring import compute_risk_score
from app.schemas.request import ScoreRequest
from app.schemas.response import ScoreResponse

class BatchProcessor:
    def __init__(self, batch_size: int = 1000, max_workers: int = 4):
        """
        Initialize batch processor.
        
        Args:
            batch_size: Number of transactions per batch
            max_workers: Maximum number of concurrent threads
        """
        self.batch_size = batch_size
        self.max_workers = max_workers
    
    def _split_transactions(self, transactions: List[Dict[str, Any]]) -> List[List[Dict[str, Any]]]:
        """Split transactions into batches based on date ranges"""
        df = pd.DataFrame(transactions)
        df['date'] = pd.to_datetime(df['date'])
        df = df.sort_values('date')
        
        # Split into roughly equal batches
        return [batch.to_dict('records') for _, batch in df.groupby(df.index // self.batch_size)]
    
    def _process_batch(self, batch: List[Dict[str, Any]], user_id: str) -> ScoreResponse:
        """Process a single batch of transactions"""
        validate_transactions(batch)
        request = ScoreRequest(user_id=user_id, transactions=batch)
        return compute_risk_score(request)
    
    def process_transactions(self, user_id: str, transactions: List[Dict[str, Any]]) -> ScoreResponse:
        """
        Process large transaction sets in batches.
        
        Args:
            user_id: User identifier
            transactions: List of transactions to process
            
        Returns:
            Combined ScoreResponse for all transactions
        """
        if len(transactions) <= self.batch_size:
            return self._process_batch(transactions, user_id)
        
        batches = self._split_transactions(transactions)
        batch_results = []
        
        with ThreadPoolExecutor(max_workers=self.max_workers) as executor:
            future_to_batch = {
                executor.submit(self._process_batch, batch, user_id): i 
                for i, batch in enumerate(batches)
            }
            
            for future in as_completed(future_to_batch):
                try:
                    result = future.result()
                    batch_results.append(result)
                except Exception as e:
                    batch_num = future_to_batch[future]
                    print(f"Error processing batch {batch_num}: {str(e)}")
                    raise
        
        # Combine results
        total_score = sum(r.score * len(batches[i]) for i, r in enumerate(batch_results))
        total_transactions = sum(len(b) for b in batches)
        final_score = int(total_score / total_transactions)
        
        # Combine feature breakdowns (weighted average)
        combined_breakdown = {}
        for feature in batch_results[0].breakdown.keys():
            weighted_sum = sum(
                r.breakdown[feature] * len(batches[i]) 
                for i, r in enumerate(batch_results)
            )
            combined_breakdown[feature] = weighted_sum / total_transactions
        
        # Determine final band
        band = (
            "On life support" if final_score < 590 else
            "Needs attention" if final_score < 690 else
            "Could be better / Nearly there" if final_score < 770 else
            "High 5's" if final_score < 840 else
            "Sterling job"
        )
        
        return ScoreResponse(
            score=final_score,
            band=band,
            breakdown=combined_breakdown
        )
