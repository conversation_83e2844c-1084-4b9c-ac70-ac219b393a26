# Test Payloads for Different Financial Scenarios

These test payloads demonstrate various financial situations and their expected impact on the risk score. Each scenario is designed to test different aspects of the scoring model.

## 1. High-Income Stable Employment

This scenario represents a person with:
- High, stable monthly income
- Regular utility payments
- Good debt management
- Strong savings behavior
- Mix of essential and discretionary spending

```json
{
    "total": 24,
    "transactions": [
        {
            "_id": "test_salary_1",
            "amount": 500000,
            "balance": 2500000,
            "currency": "USD",
            "date": "2025-06-15T01:33:06.305Z",
            "narration": "SALARY CREDIT FROM TECH CORP GLOBAL",
            "type": "credit"
        },
        {
            "_id": "test_util_1",
            "amount": 15000,
            "balance": 2485000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "ELECTRICITY BILL PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_util_2",
            "amount": 8000,
            "balance": 2477000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "WATER UTILITY PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_mortgage",
            "amount": 150000,
            "balance": 2327000,
            "currency": "USD",
            "date": "2025-06-02T01:33:06.305Z",
            "narration": "MORTGAGE PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_investment",
            "amount": 100000,
            "balance": 2227000,
            "currency": "USD",
            "date": "2025-06-03T01:33:06.305Z",
            "narration": "INVESTMENT TRANSFER TO MUTUAL FUND",
            "type": "debit"
        },
        {
            "_id": "test_grocery",
            "amount": 45000,
            "balance": 2182000,
            "currency": "USD",
            "date": "2025-06-07T01:33:06.305Z",
            "narration": "PAYMENT AT WHOLE FOODS MARKET",
            "type": "debit"
        }
    ]
}

## 2. Gig Economy / Variable Income

This scenario represents a person with:
- Multiple irregular income sources
- Variable payment amounts
- Regular essential expenses
- Some late utility payments
- Higher liquidity needs

```json
{
    "total": 24,
    "transactions": [
        {
            "_id": "test_freelance_1",
            "amount": 150000,
            "balance": 800000,
            "currency": "USD",
            "date": "2025-06-15T01:33:06.305Z",
            "narration": "PAYMENT FROM UPWORK - PROJECT A",
            "type": "credit"
        },
        {
            "_id": "test_freelance_2",
            "amount": 75000,
            "balance": 875000,
            "currency": "USD",
            "date": "2025-06-10T01:33:06.305Z",
            "narration": "PAYMENT FROM FIVERR - DESIGN WORK",
            "type": "credit"
        },
        {
            "_id": "test_rideshare",
            "amount": 85000,
            "balance": 960000,
            "currency": "USD",
            "date": "2025-06-20T01:33:06.305Z",
            "narration": "UBER DRIVER PAYMENT",
            "type": "credit"
        },
        {
            "_id": "test_util_late",
            "amount": 18000,
            "balance": 942000,
            "currency": "USD",
            "date": "2025-06-12T01:33:06.305Z",
            "narration": "LATE ELECTRICITY BILL PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_rent",
            "amount": 200000,
            "balance": 742000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "MONTHLY RENT PAYMENT",
            "type": "debit"
        }
    ]
}

## 3. High Risk Profile

This scenario represents a person with:
- Inconsistent income
- Multiple loan payments
- Missed/late payments
- High debt-to-income ratio
- Poor savings behavior

```json
{
    "total": 24,
    "transactions": [
        {
            "_id": "test_salary_irregular",
            "amount": 250000,
            "balance": 300000,
            "currency": "USD",
            "date": "2025-06-15T01:33:06.305Z",
            "narration": "SALARY PAYMENT",
            "type": "credit"
        },
        {
            "_id": "test_personal_loan",
            "amount": 75000,
            "balance": 225000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "PERSONAL LOAN PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_credit_card",
            "amount": 95000,
            "balance": 130000,
            "currency": "USD",
            "date": "2025-06-06T01:33:06.305Z",
            "narration": "CREDIT CARD PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_util_missed",
            "amount": 25000,
            "balance": 105000,
            "currency": "USD",
            "date": "2025-06-20T01:33:06.305Z",
            "narration": "UTILITY BILL PAYMENT - OVERDUE",
            "type": "debit"
        },
        {
            "_id": "test_payday_loan",
            "amount": 50000,
            "balance": 55000,
            "currency": "USD",
            "date": "2025-06-10T01:33:06.305Z",
            "narration": "PAYDAY LOAN PAYMENT",
            "type": "debit"
        }
    ]
}

## 4. Mixed Income Sources with Good Management

This scenario represents a person with:
- Primary salary plus rental income
- Regular savings
- Consistent bill payments
- Good debt management
- Diversified income streams

```json
{
    "total": 24,
    "transactions": [
        {
            "_id": "test_primary_salary",
            "amount": 300000,
            "balance": 1500000,
            "currency": "USD",
            "date": "2025-06-15T01:33:06.305Z",
            "narration": "SALARY CREDIT FROM EMPLOYER INC",
            "type": "credit"
        },
        {
            "_id": "test_rental_income",
            "amount": 150000,
            "balance": 1650000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "RENTAL INCOME PROPERTY A",
            "type": "credit"
        },
        {
            "_id": "test_dividend",
            "amount": 25000,
            "balance": 1675000,
            "currency": "USD",
            "date": "2025-06-10T01:33:06.305Z",
            "narration": "DIVIDEND PAYMENT - INVESTMENT FUND",
            "type": "credit"
        },
        {
            "_id": "test_mortgage",
            "amount": 120000,
            "balance": 1555000,
            "currency": "USD",
            "date": "2025-06-02T01:33:06.305Z",
            "narration": "MORTGAGE PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_savings",
            "amount": 100000,
            "balance": 1455000,
            "currency": "USD",
            "date": "2025-06-03T01:33:06.305Z",
            "narration": "TRANSFER TO SAVINGS ACCOUNT",
            "type": "debit"
        },
        {
            "_id": "test_util_1",
            "amount": 12000,
            "balance": 1443000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "UTILITY PAYMENT - ELECTRICITY",
            "type": "debit"
        }
    ]
}

## 5. Young Professional / Early Career

This scenario represents a person with:
- Entry-level salary
- Student loan payments
- Careful budget management
- Building savings
- Limited credit history

```json
{
    "total": 24,
    "transactions": [
        {
            "_id": "test_entry_salary",
            "amount": 200000,
            "balance": 250000,
            "currency": "USD",
            "date": "2025-06-15T01:33:06.305Z",
            "narration": "SALARY CREDIT - ENTRY LEVEL",
            "type": "credit"
        },
        {
            "_id": "test_student_loan",
            "amount": 35000,
            "balance": 215000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "STUDENT LOAN PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_rent",
            "amount": 80000,
            "balance": 135000,
            "currency": "USD",
            "date": "2025-06-02T01:33:06.305Z",
            "narration": "RENT PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_utilities",
            "amount": 10000,
            "balance": 125000,
            "currency": "USD",
            "date": "2025-06-05T01:33:06.305Z",
            "narration": "UTILITIES PAYMENT",
            "type": "debit"
        },
        {
            "_id": "test_savings",
            "amount": 20000,
            "balance": 105000,
            "currency": "USD",
            "date": "2025-06-03T01:33:06.305Z",
            "narration": "AUTOMATIC SAVINGS TRANSFER",
            "type": "debit"
        }
    ]
}
