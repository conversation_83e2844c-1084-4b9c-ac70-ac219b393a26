"""
Income analysis module.
"""
import pandas as pd
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from typing import List, Dict, Any

def _normalize_date(date_str: str) -> pd.Timestamp:
    """Convert date string to UTC timestamp and strip timezone info"""
    return pd.to_datetime(date_str).tz_localize(None)

def _generate_missing_months(df: pd.DataFrame, lookback_months: int = 6) -> pd.DataFrame:
    """Generate a DataFrame with missing months filled with zero income"""
    if df.empty:
        return df

    # Get the date range after normalizing dates
    df['date'] = df['date'].apply(_normalize_date)
    max_date = df['date'].max()
    min_date = max_date - relativedelta(months=lookback_months-1)
    min_date = min_date.replace(day=1)
    
    # Create complete month range
    date_range = pd.date_range(
        start=min_date,
        end=max_date,
        freq='MS'  # Month Start frequency
    )
    
    # Create template DataFrame with all months and normalize dates
    template = pd.DataFrame({'date': date_range})
    template['month'] = template['date'].dt.to_period('M')
    
    # Merge with actual data after converting to period
    df['month'] = df['date'].dt.to_period('M')
    result = template.merge(
        df.groupby('month')['amount'].sum().reset_index(),
        on='month',
        how='left'
    )
    
    # Fill missing values with 0
    result['amount'] = result['amount'].fillna(0)
    
    return result

def compute_income_stability(transactions: List[Dict[str, Any]]) -> float:
    """
    Consistency of income over 6 months from one or more sources.
    Returns a normalized stability score between 0 and 1.
    """
    if not transactions:
        return 0.0

    # Convert to DataFrame
    df = pd.DataFrame([vars(t) if hasattr(t, "__dict__") else t for t in transactions])
    df['date'] = pd.to_datetime(df['date'])
    
    # Filter for income (credits)
    income_df = df[df['type'].str.lower() == 'credit'].copy()
    if income_df.empty:
        return 0.0

    # Fill in missing months with zero income
    monthly_data = _generate_missing_months(income_df)
    
    if monthly_data.empty:
        return 0.0

    # Calculate stability metrics
    mean_income = monthly_data['amount'].mean()
    std_income = monthly_data['amount'].std()
    
    if mean_income == 0:
        return 0.0
        
    # Stability: 1 - (std/mean), clipped to [0,1]
    stability = 1 - (std_income / mean_income)
    return max(0.0, min(1.0, stability))

def compute_income_volatility(transactions: List[Dict[str, Any]]) -> float:
    """
    Standard deviation of monthly income divided by mean income.
    Returns a normalized volatility score between 0 and 1 (lower is better).
    """
    if not transactions:
        return 1.0

    # Convert to DataFrame
    df = pd.DataFrame([vars(t) if hasattr(t, "__dict__") else t for t in transactions])
    df['date'] = pd.to_datetime(df['date'])
    
    # Filter for income (credits)
    income_df = df[df['type'].str.lower() == 'credit'].copy()
    if income_df.empty:
        return 1.0

    # Fill in missing months with zero income
    monthly_data = _generate_missing_months(income_df)
    
    if monthly_data.empty:
        return 1.0

    mean_income = monthly_data['amount'].mean()
    std_income = monthly_data['amount'].std()
    
    if mean_income == 0:
        return 1.0
        
    # Normalize volatility score
    volatility = std_income / mean_income
    return max(0.0, min(1.0, volatility))

def compute_recurring_income_ratio(transactions: List[Dict[str, Any]]) -> float:
    """
    Ratio of recurring (predictable) income to total income.
    Considers both frequency and consistency of amounts.
    Returns a value between 0 and 1.
    """
    if not transactions:
        return 0.0

    df = pd.DataFrame([vars(t) if hasattr(t, "__dict__") else t for t in transactions])
    df['date'] = pd.to_datetime(df['date'])
    income_df = df[df['type'].str.lower() == 'credit'].copy()
    
    if income_df.empty:
        return 0.0

    # Prepare data for pattern analysis
    income_df['month'] = income_df['date'].dt.to_period('M')
    income_df['day_of_month'] = income_df['date'].dt.day
    
    # Use narration if available, fallback to category
    group_col = 'narration' if 'narration' in income_df.columns else 'category'
    if group_col not in income_df.columns:
        return 0.0

    # Group by income source
    source_stats = []
    for source in income_df[group_col].unique():
        source_df = income_df[income_df[group_col] == source]
        
        # Check frequency (minimum 3 months)
        months_present = source_df['month'].nunique()
        if months_present < 3:
            continue
            
        # Check amount consistency
        amounts = source_df['amount']
        amount_mean = amounts.mean()
        amount_std = amounts.std()
        amount_cv = amount_std / amount_mean if amount_mean > 0 else float('inf')
        
        # Check timing consistency
        days = source_df['day_of_month']
        day_std = days.std()
        
        # Score the recurrence pattern (0 to 1)
        frequency_score = min(1.0, months_present / 6)  # Normalize to 6 months
        consistency_score = 1.0 if amount_cv < 0.2 else 0.5  # Allow 20% variation
        timing_score = 1.0 if day_std < 5 else 0.5  # Allow 5 days variation
        
        recurrence_score = (frequency_score + consistency_score + timing_score) / 3
        
        if recurrence_score >= 0.6:  # Minimum threshold for recurring
            source_stats.append({
                'source': source,
                'amount': amount_mean * months_present,  # Total contribution
                'score': recurrence_score
            })

    # Calculate recurring income
    recurring_income = sum(stat['amount'] * stat['score'] for stat in source_stats)
    total_income = income_df['amount'].sum()
    
    if total_income == 0:
        return 0.0
        
    return recurring_income / total_income

def extract_monthly_income(transactions: List[Dict[str, Any]]) -> pd.Series:
    """
    Returns a pandas Series of monthly total income (credits).
    """
    if not transactions:
        return pd.Series(dtype=float)

    df = pd.DataFrame([vars(t) if hasattr(t, "__dict__") else t for t in transactions])
    df['date'] = pd.to_datetime(df['date'])
    income_df = df[df['type'].str.lower() == 'credit']
    
    if income_df.empty:
        return pd.Series(dtype=float)
        
    # Fill in missing months
    monthly_data = _generate_missing_months(income_df)
    return pd.Series(monthly_data['amount'].values, index=monthly_data['month'])
