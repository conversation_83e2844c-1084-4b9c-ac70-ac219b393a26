from app.features.income import (
    compute_income_stability,
    compute_income_volatility,
    compute_recurring_income_ratio,
)
from app.features.debt import (
    compute_debt_to_income_ratio as compute_dti,
    compute_eti,
    compute_fixed_obligation_ratio,
    compute_repayment_consistency_ratio
)
from app.features.liquidity import compute_liquidity_ratio, compute_cash_flow_stability
from app.features.utilities import compute_utility_payment_frequency, compute_utility_payment_history
from app.features.payments import compute_payment_frequency_other, compute_payment_history_other, compute_payments_mix
from app.features.budget import compute_household_budget_management
from app.schemas.request import ScoreRequest
from app.schemas.response import ScoreResponse

def compute_risk_score(request: ScoreRequest) -> ScoreResponse:
    """
    Compute risk score with validation and caching.
    
    Args:
        request: ScoreRequest containing user_id and transactions
        
    Returns:
        ScoreResponse with score, band, and feature breakdown
        
    Raises:
        TransactionValidationError: If transaction data is invalid
    """
    from app.validation.transaction_validator import validate_transactions
    from app.utils.cache import cache
    
    # Validate transactions
    txns = request.transactions
    validate_transactions(txns)
      # Check cache for previous calculation
    cached_result = cache.get(txns, 'risk_score')
    if cached_result:
        return cached_result
    
    # Compute utility payment features
    payment_freq_utilities = compute_utility_payment_frequency(txns)
    payment_history_utilities = compute_utility_payment_history(txns)
    
    # Compute other payment features
    payment_freq_other = compute_payment_frequency_other(txns)
    payment_history_other = compute_payment_history_other(txns)
    payments_mix = compute_payments_mix(txns)
    
    # Compute household budget feature
    household_budget = compute_household_budget_management(txns)
    
    # Compute traditional features
    income_stability = compute_income_stability(txns)
    income_volatility = 1 - compute_income_volatility(txns)  # invert so higher is better
    recurring_income_ratio = compute_recurring_income_ratio(txns)
    dti = 1 - min(compute_dti(txns), 1)  # invert so higher is better, cap at 1
    eti = 1 - min(compute_eti(txns), 1)  # invert so higher is better
    fixed_obligation = 1 - min(compute_fixed_obligation_ratio(txns), 1)  # invert so higher is better
    repayment_consistency = compute_repayment_consistency_ratio(txns)
    liquidity = min(compute_liquidity_ratio(txns) / 3, 1.0)  # normalize: >3 is best
    cash_flow_stability = 1 - compute_cash_flow_stability(txns)  # invert    # Calculate savings history score (based on liquidity and cash flow)
    savings_history = (liquidity * 0.6 + cash_flow_stability * 0.4)
    
    weights = {
        # High Impact Components (16.67% each)
        "payment_freq_utilities": 0.1667,    # Utility payment frequency
        "payment_history_utilities": 0.1667,  # Utility payment history/consistency
        "savings_history": 0.1667,           # Based on liquidity and cash flow

        # Medium Impact Components (11.11% each)
        "payment_freq_other": 0.1111,        # Non-utility payment frequency
        "payment_history_other": 0.1111,     # Non-utility payment consistency
        "household_budget": 0.1111,          # Budget management effectiveness

        # Low Impact Component (5.56%)
        "payments_mix": 0.0556,              # Diversity and health of payment types

        # Phase 2 Component (not included in MVP scoring)
        # "social_reputation": 0.1111,       # Will be added in Phase 2
    }
    
    features = {
        "payment_freq_utilities": payment_freq_utilities,
        "payment_history_utilities": payment_history_utilities,
        "savings_history": savings_history,
        "payment_freq_other": payment_freq_other,
        "payment_history_other": payment_history_other,
        "household_budget": household_budget,
        "payments_mix": payments_mix,
        
        # Traditional metrics (for reference)
        "income_stability": income_stability,
        "income_volatility": income_volatility,
        "recurring_income_ratio": recurring_income_ratio,
        "dti": dti,
        "eti": eti,
        "fixed_obligation": fixed_obligation,
        "repayment_consistency": repayment_consistency,
        "liquidity": liquidity,
        "cash_flow_stability": cash_flow_stability,
    }    # Calculate weighted score using only the new scoring weights
    weighted_score = sum(weights[k] * features[k] for k in weights.keys())
    
    # Map to 350-900 range
    score = int(350 + weighted_score * (900 - 350))
    
    # Determine score band
    band = "Poor" if score < 500 else \
           "Fair" if score < 600 else \
           "Good" if score < 700 else \
           "Very Good" if score < 800 else \
           "Excellent"
    
    return ScoreResponse(
        score=score,
        band=band,
        breakdown={k: round(v, 4) for k, v in features.items()}
    )
    band = (
        "On life support" if score < 590 else
        "Needs attention" if score < 690 else
        "Could be better / Nearly there" if score < 770 else
        "High 5’s" if score < 840 else
        "Sterling job"
    )

    result = ScoreResponse(
        score=score,
        band=band,
        breakdown=features
    )
    
    # Cache the result
    cache.set(txns, 'risk_score', result)
    
    return result
