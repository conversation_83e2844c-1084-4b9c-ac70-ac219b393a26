"""
Test runner for Accentity Risk Model API feature functions using dummy data.
Run this file to quickly check feature calculations with sample payloads.
"""

from types import SimpleNamespace
from datetime import datetime
from app.features.income import (
    compute_income_stability,
    compute_income_volatility,
    compute_recurring_income_ratio,
)
from app.features.debt import (
    compute_dti,
    compute_eti,
    compute_fixed_obligation_ratio,
    compute_repayment_consistency_ratio,
)
from app.features.liquidity import (
    compute_liquidity_ratio,
    compute_cash_flow_stability,
)
from app.features.utilities import (
    compute_utility_payment_frequency,
    compute_utility_payment_history,
)
from app.features.payments import (
    compute_payment_frequency_other,
    compute_payment_history_other,
    compute_payments_mix,
)
from app.features.budget import compute_household_budget_management
from app.models.scoring import compute_risk_score
from app.schemas.request import ScoreRequest
from app.schemas.response import ScoreResponse

# Sample transactions covering 6 months (January to June 2025)
sample_transactions = [
    # January 2025
    {
        "date": "2025-01-01",
        "amount": 50000.0,
        "type": "credit",
        "category": "salary",
        "description": "January Salary"
    },
    {
        "date": "2025-01-03",
        "amount": 9500.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-01-05",
        "amount": 3500.0,
        "type": "debit",
        "category": "utilities",
        "description": "Water Bill"
    },
    {
        "date": "2025-01-07",
        "amount": 2500.0,
        "type": "debit",
        "category": "utilities",
        "description": "Gas Bill"
    },
    {
        "date": "2025-01-03",
        "amount": 9500.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-01-05",
        "amount": 15000.0,
        "type": "debit",
        "category": "loan",
        "description": "Loan Repayment"
    },
    {
        "date": "2025-01-10",
        "amount": 2500.0,
        "type": "debit",
        "category": "food",
        "description": "Groceries"
    },
    {
        "date": "2025-01-15",
        "amount": 4000.0,
        "type": "credit",
        "category": "side_income",
        "description": "Freelance Gig"
    },
    
    # February 2025
    {
        "date": "2025-02-01",
        "amount": 50000.0,
        "type": "credit",
        "category": "salary",
        "description": "February Salary"
    },
    {
        "date": "2025-02-03",
        "amount": 10200.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-02-05",
        "amount": 15000.0,
        "type": "debit",
        "category": "loan",
        "description": "Loan Repayment"
    },
    {
        "date": "2025-02-12",
        "amount": 2300.0,
        "type": "debit",
        "category": "food",
        "description": "Groceries"
    },
    {
        "date": "2025-02-15",
        "amount": 6000.0,
        "type": "credit",
        "category": "side_income",
        "description": "Freelance Gig"
    },

    # March 2025
    {
        "date": "2025-03-01",
        "amount": 52000.0,
        "type": "credit",
        "category": "salary",
        "description": "March Salary with Bonus"
    },
    {
        "date": "2025-03-03",
        "amount": 9800.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-03-05",
        "amount": 15000.0,
        "type": "debit",
        "category": "loan",
        "description": "Loan Repayment"
    },
    {
        "date": "2025-03-10",
        "amount": 2800.0,
        "type": "debit",
        "category": "food",
        "description": "Groceries"
    },
    {
        "date": "2025-03-20",
        "amount": 5500.0,
        "type": "credit",
        "category": "side_income",
        "description": "Freelance Gig"
    },

    # April 2025
    {
        "date": "2025-04-01",
        "amount": 50000.0,
        "type": "credit",
        "category": "salary",
        "description": "April Salary"
    },
    {
        "date": "2025-04-03",
        "amount": 10500.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-04-05",
        "amount": 15000.0,
        "type": "debit",
        "category": "loan",
        "description": "Loan Repayment"
    },
    {
        "date": "2025-04-11",
        "amount": 2600.0,
        "type": "debit",
        "category": "food",
        "description": "Groceries"
    },
    {
        "date": "2025-04-18",
        "amount": 4800.0,
        "type": "credit",
        "category": "side_income",
        "description": "Freelance Gig"
    },

    # May 2025
    {
        "date": "2025-05-01",
        "amount": 50000.0,
        "type": "credit",
        "category": "salary",
        "description": "May Salary"
    },
    {
        "date": "2025-05-03",
        "amount": 9900.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-05-05",
        "amount": 15000.0,
        "type": "debit",
        "category": "loan",
        "description": "Loan Repayment"
    },
    {
        "date": "2025-05-10",
        "amount": 2400.0,
        "type": "debit",
        "category": "food",
        "description": "Groceries"
    },
    {
        "date": "2025-05-15",
        "amount": 5200.0,
        "type": "credit",
        "category": "side_income",
        "description": "Freelance Gig"
    },

    # June 2025
    {
        "date": "2025-06-01",
        "amount": 50000.0,
        "type": "credit",
        "category": "salary",
        "description": "June Salary"
    },
    {
        "date": "2025-06-03",
        "amount": 10100.0,
        "type": "debit",
        "category": "utilities",
        "description": "Electricity Bill"
    },
    {
        "date": "2025-06-05",
        "amount": 15000.0,
        "type": "debit",
        "category": "loan",
        "description": "Loan Repayment"
    },
    {
        "date": "2025-06-10",
        "amount": 2700.0,
        "type": "debit",
        "category": "food",
        "description": "Groceries"
    },
    {
        "date": "2025-06-16",
        "amount": 5800.0,
        "type": "credit",
        "category": "side_income",
        "description": "Freelance Gig"
    }
]

# Convert dicts to objects for compatibility with feature functions
txns = [SimpleNamespace(**t) for t in sample_transactions]

print("=== Accentity Risk Model Feature Test ===")
print("\n=== Individual Features ===")
print("Income Stability:", compute_income_stability(txns))
print("Income Volatility:", compute_income_volatility(txns))
print("Recurring Income Ratio:", compute_recurring_income_ratio(txns))
print("Debt-to-Income Ratio (DTI):", compute_dti(txns))
print("Expense-to-Income Ratio (ETI):", compute_eti(txns))
print("Fixed Obligation Ratio:", compute_fixed_obligation_ratio(txns))
print("Repayment Consistency Ratio:", compute_repayment_consistency_ratio(txns))
print("Liquidity Ratio:", compute_liquidity_ratio(txns))
print("Cash Flow Stability:", compute_cash_flow_stability(txns))

print("\n=== New Features ===")
print("Utility Payment Frequency:", compute_utility_payment_frequency(txns))
print("Utility Payment History:", compute_utility_payment_history(txns))
print("Other Payment Frequency:", compute_payment_frequency_other(txns))
print("Other Payment History:", compute_payment_history_other(txns))
print("Payments Mix:", compute_payments_mix(txns))
print("Household Budget Management:", compute_household_budget_management(txns))

# Calculate final risk score
print("\n=== Final Risk Assessment ===")
request = ScoreRequest(user_id="test_user", transactions=sample_transactions)
result = compute_risk_score(request)
print(f"Risk Score: {result.score} ({result.band})")
