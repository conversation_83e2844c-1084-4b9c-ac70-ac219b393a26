import requests

class MonoClient:
    def __init__(self, api_key: str):
        self.api_key = api_key
        self.base_url = "https://api.withmono.com" #to replace with actual url tested with by <PERSON>

    def get_transactions(self, account_id: str):
        # Example stub: Replace with real Mono API call
        headers = {"mono-sec-key": self.api_key}
        url = f"{self.base_url}/accounts/{account_id}/transactions"
        response = requests.get(url, headers=headers)
        if response.status_code == 200:
            return response.json().get("data", [])
        return []
