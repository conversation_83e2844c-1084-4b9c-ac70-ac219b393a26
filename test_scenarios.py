"""
Test scenarios for Accentity Risk Model API using different financial profiles.
"""

import json
import os
from datetime import datetime
from typing import Dict, List

from app.schemas.request import ScoreRequest
from app.models.scoring import compute_risk_score

def load_test_payload(scenario_file: str) -> Dict:
    """Load test payload from JSON file"""
    file_path = os.path.join('tests', 'data', scenario_file)
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            data = json.load(f)
            print(f"\nLoaded {len(data['transactions'])} transactions from {scenario_file}")
            return data
    except Exception as e:
        print(f"\nError loading {scenario_file}: {str(e)}")
        raise

def run_scenario_test(scenario_name: str, payload: Dict) -> None:
    """Run a test scenario and print the results"""
    print(f"\n=== Testing Scenario: {scenario_name} ===")
    
    # Create score request
    request = ScoreRequest(
        user_id=f"test_{scenario_name.lower().replace(' ', '_')}",
        transactions=payload['transactions']
    )
    
    # Compute risk score
    result = compute_risk_score(request)
    
    # Print results
    print(f"Risk Score: {result.score}")
    print(f"Risk Band: {result.band}")
    print("\nFeature Breakdown:")
    for feature, value in result.breakdown.items():
        print(f"  {feature}: {value:.4f}")

def main():
    """Run all test scenarios"""
    scenarios = [
        ("High-Income Stable Employment", "high_income.json"),
        ("Gig Economy Variable Income", "gig_economy.json"),
        ("High Risk Profile", "high_risk.json"),
        ("Mixed Income Sources", "mixed_income.json"),
        ("Young Professional", "young_professional.json")
    ]
    
    print("=== Accentity Risk Model Test Scenarios ===")
    print(f"Test Date: {datetime.now()}\n")
    
    for scenario_name, json_file in scenarios:
        try:
            payload = load_test_payload(json_file)
            run_scenario_test(scenario_name, payload)
        except Exception as e:
            print(f"\nError testing {scenario_name}: {str(e)}")

if __name__ == "__main__":
    main()