"""
Validation module for transaction data quality and integrity.
"""
from datetime import datetime
from typing import List, Dict, Any, Union
from dateutil.relativedelta import relativedelta

class TransactionValidationError(Exception):
    """Custom exception for transaction validation errors"""
    pass

def _parse_date(date_str: str) -> datetime:
    """Parse date string in either ISO or YYYY-MM-DD format"""
    try:
        # Try ISO format first
        return datetime.fromisoformat(date_str.replace('Z', '+00:00'))
    except ValueError:
        try:
            # Try YYYY-MM-DD format as fallback
            return datetime.strptime(date_str, '%Y-%m-%d')
        except ValueError:
            raise TransactionValidationError(f"Invalid date format: {date_str}")

def validate_transactions(transactions: List[Union[Dict[str, Any], Any]]) -> None:
    """
    Validates a list of transactions for data quality and completeness.
    Handles both dictionary and object-style transactions.
    Raises TransactionValidationError if validation fails.
    
    Checks:
    - Date format and range
    - Amount validity (non-negative)
    - Transaction type validity
    - Minimum data period requirements
    """
    if not transactions:
        raise TransactionValidationError("No transactions provided")
    
    # Date validation
    dates = []
    for idx, txn in enumerate(transactions):
        try:
            # Handle both dict and object access
            date_str = txn.date if hasattr(txn, 'date') else txn['date']
            date = _parse_date(date_str)
            dates.append(date)
        except TransactionValidationError as e:
            raise TransactionValidationError(f"Transaction {idx}: {str(e)}")
        except (AttributeError, KeyError):
            raise TransactionValidationError(
                f"Missing date in transaction {idx}"
            )
    
    # Check minimum period coverage (3 months)
    if dates:
        min_date, max_date = min(dates), max(dates)
        months_covered = (max_date.year - min_date.year) * 12 + max_date.month - min_date.month
        if months_covered < 3:
            raise TransactionValidationError(
                f"Insufficient transaction history. Need minimum 3 months, got {months_covered} months"
            )
    
    for idx, txn in enumerate(transactions):
        # Amount validation
        try:
            amount = txn.amount if hasattr(txn, 'amount') else txn['amount']
        except (AttributeError, KeyError):
            raise TransactionValidationError(f"Missing amount in transaction {idx}")
            
        if not isinstance(amount, (int, float)) or amount < 0:
            raise TransactionValidationError(
                f"Invalid amount in transaction {idx}: {amount}. "
                "Amount must be a non-negative number"
            )
        
        # Transaction type validation
        try:
            txn_type = (txn.type if hasattr(txn, 'type') else txn['type']).lower()
        except (AttributeError, KeyError):
            raise TransactionValidationError(f"Missing type in transaction {idx}")
            
        if txn_type not in ['credit', 'debit']:
            raise TransactionValidationError(
                f"Invalid transaction type in transaction {idx}: {txn_type}. "
                "Must be either 'credit' or 'debit'"
            )

def validate_transaction_patterns(transactions: List[Dict[str, Any]]) -> Dict[str, Any]:
    """
    Analyzes transaction patterns for data quality insights.
    Returns a dict of pattern metrics.
    """
    metrics = {
        'total_transactions': len(transactions),
        'credits': sum(1 for t in transactions if t['type'].lower() == 'credit'),
        'debits': sum(1 for t in transactions if t['type'].lower() == 'debit'),
        'categories': set(t.get('category') for t in transactions if t.get('category')),
        'has_descriptions': all('description' in t for t in transactions),
    }
    return metrics
