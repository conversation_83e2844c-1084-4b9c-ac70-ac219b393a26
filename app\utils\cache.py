"""
Cache module for optimizing expensive calculations.
"""
from typing import Any, Dict, List, Union
from datetime import datetime, timedelta
import hashlib
import json

class ResultCache:
    """Simple in-memory cache with time-based expiration"""
    
    def __init__(self, ttl_minutes: int = 60):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._ttl = timedelta(minutes=ttl_minutes)
    
    def _generate_key(self, transactions: List[Union[Dict[str, Any], Any]], func_name: str) -> str:
        """Generate a cache key from transactions and function name"""
        def get_tx_value(tx, attr):
            """Helper to get value from either dict or object"""
            return getattr(tx, attr) if hasattr(tx, attr) else tx[attr]
        
        # Convert transactions to serializable format
        tx_list = []
        for tx in transactions:
            tx_dict = {
                'date': str(get_tx_value(tx, 'date')),
                'amount': float(get_tx_value(tx, 'amount')),
                'type': str(get_tx_value(tx, 'type'))
            }
            tx_list.append(tx_dict)
        
        # Sort transactions to ensure consistent hashing
        sorted_txns = sorted(tx_list, key=lambda x: (x['date'], x['amount'], x['type']))
        txn_str = json.dumps(sorted_txns, sort_keys=True)
        key_str = f"{func_name}:{txn_str}"
        return hashlib.md5(key_str.encode()).hexdigest()
    
    def get(self, transactions: List[Union[Dict[str, Any], Any]], func_name: str) -> Any:
        """Get cached result if available and not expired"""
        key = self._generate_key(transactions, func_name)
        if key in self._cache:
            entry = self._cache[key]
            if datetime.now() - entry['timestamp'] < self._ttl:
                return entry['result']
            else:
                del self._cache[key]
        return None
    
    def set(self, transactions: List[Union[Dict[str, Any], Any]], func_name: str, result: Any) -> None:
        """Cache a calculation result"""
        key = self._generate_key(transactions, func_name)
        self._cache[key] = {
            'result': result,
            'timestamp': datetime.now()
        }
    
    def clear(self) -> None:
        """Clear all cached results"""
        self._cache.clear()

# Global cache instance
cache = ResultCache()
