from pydantic import BaseModel
from typing import List, Optional

class Transaction(BaseModel):
    date: str
    amount: float
    type: str  # credit/debit
    category: Optional[str] = None
    description: Optional[str] = None

class ScoreRequest(BaseModel):
    user_id: str
    transactions: List[Transaction]
    # Optionally add assets, liabilities, etc.

# Example structure of the input (ScoreRequest) expected by the /score endpoint
example_request = {
    "user_id": "string",                # Unique user identifier
    "transactions": [
        {
            "date": "YYYY-MM-DD",           # Transaction date (ISO format)
            "amount": 0.0,                  # Transaction amount (float)
            "type": "credit|debit",         # Transaction type: "credit" or "debit"
            "category": "string",           # (Optional) Category, e.g. "salary", "utilities", "loan"
            "description": "string"         # (Optional) Description or narration
        }
        
    ]
}
