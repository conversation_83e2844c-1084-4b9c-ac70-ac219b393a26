"""
Liquidity analysis module.
"""
import pandas as pd
from typing import List, Dict, Any

def _normalize_date(date_str: str) -> pd.Timestamp:
    """Convert date string to UTC timestamp and strip timezone info"""
    return pd.to_datetime(date_str).tz_localize(None)

def compute_liquidity_ratio(transactions):
    """
    Liquidity Ratio: Total Liquid Assets ÷ Average Monthly Expenses
    For simplicity, assume cash balance is sum of all credits - sum of all debits.
    """
    if not transactions:
        return 0.0
    df = pd.DataFrame([vars(t) if hasattr(t, "__dict__") else t for t in transactions])
    df['date'] = df['date'].apply(_normalize_date)
    credits = df[df['type'].str.lower() == 'credit']['amount'].sum()
    debits = df[df['type'].str.lower() == 'debit']['amount'].sum()
    liquid_assets = credits - debits
    
    # Create a copy for expense calculations
    expense_df = df[df['type'].str.lower() == 'debit'].copy()
    expense_df.loc[:, 'month'] = expense_df['date'].dt.to_period('M')
    monthly_expense = expense_df.groupby('month')['amount'].sum()
    avg_monthly_expense = monthly_expense.mean() if not monthly_expense.empty else 1.0
    if avg_monthly_expense == 0:
        return 0.0
    return liquid_assets / avg_monthly_expense

def compute_cash_flow_stability(transactions):
    """
    Cash Flow Stability Ratio: std(monthly net cash flow) / mean(monthly net cash flow)
    Returns a value between 0 and 1 (lower is better).
    """
    if not transactions:
        return 1.0
    df = pd.DataFrame([vars(t) if hasattr(t, "__dict__") else t for t in transactions])
    df['date'] = df['date'].apply(_normalize_date)
    df['month'] = df['date'].dt.to_period('M')
    credits = df[df['type'].str.lower() == 'credit'].groupby('month')['amount'].sum()
    debits = df[df['type'].str.lower() == 'debit'].groupby('month')['amount'].sum()
    months = sorted(set(credits.index) | set(debits.index))
    net_cash_flow = pd.Series([credits.get(m, 0) - debits.get(m, 0) for m in months], index=months)
    if net_cash_flow.mean() == 0:
        return 1.0
    ratio = net_cash_flow.std() / abs(net_cash_flow.mean())
    return max(0.0, min(1.0, ratio))


