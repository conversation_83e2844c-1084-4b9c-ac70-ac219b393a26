# Accentity Risk Model API

## Overview

The Accentity Risk Model API is a backend service for calculating financial risk scores for individuals in Africa, using transaction data (eg. starting from Mono API). The risk score helps assess financial inclusion and creditworthiness, especially for those without traditional credit profiles.

The API ingests transaction data, computes a range of financial health features, and returns a risk score (350–900), a risk band, and a detailed feature breakdown.

---

## Features

- Ingests transaction data from Mono or similar sources
- Calculates key financial health metrics:
  - Income stability & volatility
  - Recurring income ratio
  - Debt-to-income ratio (DTI)
  - Expense-to-income ratio (ETI)
  - Liquidity ratio
  - Cash flow stability
  - Fixed obligation ratio
  - Repayment consistency ratio
- Returns a risk score and band
- Provides a feature breakdown for transparency
- RESTful API built with FastAPI

---

## Project Structure

```
accentity-risk-model/
├── app/
│   ├── main.py
│   ├── routers/
│   │   ├── scoring.py
│   │   └── health.py
│   ├── schemas/
│   │   ├── request.py
│   │   └── response.py
│   ├── ingestion/
│   │   └── client.py
│   ├── features/
│   │   ├── income.py
│   │   ├── debt.py
│   │   └── liquidity.py
│   ├── models/
│   │   └── scoring.py
│   └── ...
├── requirements.txt
├── README.md
└── ...
```

---

## Setup & Installation

1. **Clone the repository:**

   ```sh
   git clone https://github.com/your-org/accentity-risk-model.git
   cd accentity-risk-model
   ```
2. **Install dependencies:**

   ```sh
   pip install -r requirements.txt
   ```
3. **Run the API:**

   ```sh
   uvicorn app.main:app --reload
   ```

---

## API Endpoints

### Health Check

- **GET** `/health`
- Returns: `{"status": "ok"}`

### Risk Scoring

- **POST** `/score`
- **Request Body:** See [Input Structure](#input-structure)
- **Response:** See [Output Structure](#output-structure)

---

## Input Structure

Example JSON payload for `/score`:

```json
{
  "user_id": "user_12345",
  "transactions": [
    {
      "date": "2024-04-01",
      "amount": 50000.0,
      "type": "credit",
      "category": "salary",
      "description": "April Salary"
    },
    {
      "date": "2024-04-03",
      "amount": 10000.0,
      "type": "debit",
      "category": "utilities",
      "description": "Electricity Bill"
    }
    // ... more transactions ...
  ]
}
```

**Field Descriptions:**

- `user_id`: Unique identifier for the user.
- `transactions`: List of transaction objects.
  - `date`: Transaction date (YYYY-MM-DD).
  - `amount`: Transaction amount (float).
  - `type`: `"credit"` or `"debit"`.
  - `category`: (Optional) Category, e.g., salary, utilities, loan, etc.
  - `description`: (Optional) Description or narration.

---

## Output Structure

Example response from `/score`:

```json
{
  "score": 720,
  "band": "Could be better / Nearly there",
  "breakdown": {
    "income_stability": 0.85,
    "income_volatility": 0.90,
    "recurring_income_ratio": 0.75,
    "dti": 0.65,
    "eti": 0.70,
    "liquidity": 0.55,
    "cash_flow_stability": 0.80,
    "fixed_obligation_ratio": 0.40,
    "repayment_consistency_ratio": 1.0
  }
}
```

**Field Descriptions:**

- `score`: Risk score (350–900).
- `band`: Risk band (e.g., "Needs attention", "Sterling job").
- `breakdown`: Dictionary of feature names and their normalized values (0–1).

---

## How It Works

1. **Ingest**: The API receives transaction data (typically from Mono).
2. **Feature Engineering**: Extracts financial health metrics from the data.
3. **Scoring**: Aggregates features using weighted logic to produce a risk score.
4. **Output**: Returns the score, band, and feature breakdown for transparency.

---

## Testing

- Unit and integration tests should be added in a `tests/` directory.
- Use sample payloads as shown above for endpoint testing.

---

## Notes for Engineers

- All feature calculations are in `app/features/`.
- The scoring logic and weightings are in `app/models/scoring.py`.
- The API is stateless and does not persist user data by default.
- Extendable for new features, more granular categories, or additional endpoints.
- For production, add authentication, logging, and error handling will be as needed.

---

## License

---

## Contact

For questions or contributions, contact @joachimasare, ML Engineering team lead or open an issue in the repository.
